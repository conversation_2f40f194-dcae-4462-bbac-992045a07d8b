{"common": {"appName": "Paws & Whiskers", "tagline": "Find your perfect feline companion", "navigation": {"home": "Home", "cats": "Cats", "clinics": "Clinics", "about": "About", "contact": "Contact", "login": "<PERSON><PERSON>", "register": "Register", "profile": "Profile", "dashboard": "Dashboard", "logout": "Logout"}, "admin": {"dashboard": "Admin Dashboard"}, "error": "Error", "any": "Any", "loading": "Loading", "placeholders": {"user": "User", "cat": "Cat", "image": "Image"}, "yes": "Yes", "no": "No", "edit": "Edit", "user": "User", "anonymous": "Anonymous", "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "home": {"hero": {"title": "Find Your Perfect Companion", "subtitle": "Adopt a cat and change two lives forever - theirs and yours.", "description": "Connect with adorable cats looking for their forever homes. Every whisker, every purr, every gentle paw is waiting to bring joy to your life.", "cta": "Find a Cat"}, "search": {"placeholder": "Search for cats...", "button": "Search"}, "location": {"unknown": "Location not specified"}, "howItWorks": {"title": "How It Works", "subtitle": "Finding your perfect feline companion is simple and secure", "step1": {"title": "Find a Cat", "description": "Browse through hundreds of adorable cats"}, "step2": {"title": "Chat with Owner", "description": "Connect directly with shelters and caretakers"}, "step3": {"title": "Complete Adoption", "description": "Meet your new family member"}}, "mission": {"title": "Our Mission", "description": "We connect loving homes with cats in need. Our platform brings together rescuers, adopters, and veterinary clinics to ensure every cat finds their forever home.", "disclaimer": "Buying and selling cats is strictly prohibited - we support adoption only."}, "featured": {"title": "Featured Cats", "subtitle": "Meet some of our adorable cats looking for a home", "viewAll": "View All Cats"}, "stats": {"title": "Making a Difference", "adoptions": "Cats Adopted", "rescues": "Rescues", "volunteers": "Volunteers", "clinics": "Partner Clinics"}, "roles": {"title": "How You Can Help", "subtitle": "Join our community and make a difference in the lives of cats in need", "adopter": {"title": "For Adopters", "description": "Find your perfect feline companion and give them a loving forever home.", "cta": "Browse Cats"}, "rescuer": {"title": "For Rescuers", "description": "List cats for adoption and connect with potential adopters.", "cta": "Register as Rescuer"}, "clinic": {"title": "For Clinics", "description": "Create a profile for your veterinary clinic and help cats find homes.", "cta": "Register as Clinic"}}}, "cats": {"title": "Available Cats", "description": "Find your perfect feline companion from our selection of cats available for adoption", "viewAll": "View All Cats", "filters": {"title": "Filters", "age": "Age", "ageRange": "Age Range", "years": "years", "to": "to", "gender": "Gender", "male": "Male", "female": "Female", "breed": "Breed", "breeds": "breeds", "location": "Location", "wilaya": "<PERSON><PERSON><PERSON>", "wilayas": "wilayas", "commune": "<PERSON><PERSON><PERSON>", "communes": "communes", "specialNeeds": "Special Needs", "vaccinated": "Vaccinated", "neutered": "Neutered", "available": "Available", "apply": "Apply Filters", "clear": "Clear Filters", "resetAll": "Reset All", "moreFilters": "More Filters", "sortBy": "Sort By", "selectBreed": "Select a breed", "selectWilaya": "Select a wilaya", "selectCommune": "Select a commune", "selectWilayaFirst": "Select wilaya first"}, "sort": {"newest": "Newest", "oldest": "Oldest", "nameAZ": "Name A-Z", "nameZA": "Name Z-A"}, "card": {"age": "Age", "gender": "Gender", "location": "Location", "edit": "Edit", "viewDetails": "View Details", "addToFavorites": "Add to Favorites", "removeFromFavorites": "Remove from Favorites"}, "status": {"available": "Available", "pending": "Pending", "adopted": "Adopted", "fostered": "Fostered", "draft": "Draft", "draftMessage": "Draft - not visible to others", "approved": "Approved", "rejected": "Rejected", "unavailable": "Unavailable", "changeStatus": "Change Status", "updateSuccess": "Status Updated", "updateDescription": "Cat status changed to {status}", "updateError": "Failed to update status"}, "actions": {"publish": "Publish", "unpublish": "Unpublish"}, "errors": {"failedToLoad": "Failed to load cats", "noFeatured": "No featured cats available", "loadingError": "Error loading cats", "genericError": "An error occurred. Please try again."}, "loading": "Loading cats...", "retry": "Retry", "noCatsFound": "No cats found", "adjustFilters": "Try adjusting your filters to find more cats.", "search": {"placeholder": "Search by name, breed, location...", "clear": "Clear search", "resultsCount": "Found {count} cats for \"{query}\"", "noResultsFor": "No cats found for \"{query}\"", "tryDifferentTerms": "Try different search terms or adjust your filters.", "suggestions": {"tryDifferent": "Try different search terms:", "checkSpelling": "Check your spelling", "useGeneral": "Use more general terms", "tryBreed": "Try searching by breed name", "tryLocation": "Try searching by location"}}, "gallery": {"photoPlaceholder": "Cat Photo", "thumbnailPlaceholder": "Photo", "mainPhoto": "main photo", "photo": "photo", "fullSizePhoto": "full size photo"}, "notFound": "Cat Not Found", "notFoundDescription": "The requested cat could not be found.", "adoption": "Cat Adoption", "adoptNow": "<PERSON><PERSON><PERSON>", "today": "today", "share": "Share", "breedMixed": "Mixed", "viewProfile": "View Profile", "postedByShelter": "Posted by shelter staff", "location": "Location", "mapLocationFor": "Map location for", "details": {"about": "About", "story": "Story", "health": "Health", "noDescription": "No description available.", "noLocation": "Location not specified", "postedOn": "Posted on: {date}", "noStory": "No story available for this cat yet."}, "adoptionFee": "Adoption Fee", "startChatToAdopt": "Start Chat to Adopt", "askQuestion": "Ask a Question", "goodWith": "Good With", "kids": "Kids", "otherPets": "Other pets", "activeFamilies": "Active families", "currentCaretaker": "Current Caretaker", "size": "Size", "small": "Small", "medium": "Medium", "large": "Large", "breed": "Breed", "age": "Age", "gender": "Gender", "spayedNeutered": "Spayed/Neutered", "healthStatus": "Health Status", "backToAllCats": "Back to all cats", "upToDate": "up to date", "favorite": "Favorite", "edit": {"title": "Edit Cat", "description": "Edit cat listing"}, "new": {"title": "Add New Cat", "description": "List a new cat for adoption"}, "drafts": "Drafts"}, "profile": {"title": "Profile", "description": "Manage your profile, listed cats, and favorites", "overview": "Overview", "overviewDescription": "Your dashboard overview and quick stats", "notifications": "Notifications", "notificationsDescription": "Manage your notification preferences", "memberSince": "Member since", "settings": {"title": "Settings", "description": "Manage your account settings and preferences", "profileInfo": "Profile Information", "password": "Password", "updatePersonalInfo": "Update your personal information", "profilePicture": "Profile Picture", "profilePictureDescription": "Upload a photo to personalize your account", "name": "Name", "namePlaceholder": "Your name", "email": "Email", "emailDescription": "Your email address cannot be changed", "bio": "Bio", "bioPlaceholder": "Tell us a bit about yourself", "bioDescription": "This will be displayed on your public profile", "location": "Location", "locationPlaceholder": "City, State", "wilaya": "<PERSON><PERSON><PERSON>", "wilayaPlaceholder": "Select wilaya", "commune": "<PERSON><PERSON><PERSON>", "communePlaceholder": "Select commune", "selectWilayaFirst": "Select wilaya first", "loadingWilayas": "Loading wilayas...", "loadingCommunes": "Loading communes...", "phone": "Phone", "phonePlaceholder": "Your phone number", "saving": "Saving...", "saveChanges": "Save Changes", "changePassword": "Change Password", "changePasswordDescription": "Update your password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "passwordRequirement": "Password must be at least 8 characters long", "updating": "Updating...", "updateProfile": "Update Profile", "updatePassword": "Update Password", "profileUpdated": "Profile updated", "profileUpdatedDescription": "Your profile information has been updated.", "profileUpdateError": "Your profile couldn't be updated. Please try again.", "passwordUpdated": "Password updated", "passwordUpdatedDescription": "Your password has been changed successfully.", "passwordUpdateError": "Your password couldn't be updated. Please try again.", "notifications": "Notification Preferences", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive updates about your favorite cats and messages", "adoptionUpdates": "Adoption Updates", "adoptionUpdatesDesc": "Get notified when cats you're interested in are adopted", "comingSoon": "Full settings functionality coming soon. Contact support for account changes."}, "favorites": "Favorites", "favoritesDescription": "Your favorite cats and saved listings", "listedCatsDescription": "Manage your listed cats for adoption", "servicesDescription": "Manage the services your clinic offers", "clinicSettings": "Clinic Settings", "clinicSettingsDescription": "Manage your clinic profile and information", "favorite": "Favorite", "rescuer": "Rescuer", "adopter": "Adopter", "viewProfile": "View Profile", "healthInformation": "Health Information", "vaccinated": "Vaccinated", "notVaccinated": "Not Vaccinated", "neutered": "Neutered/Spayed", "notNeutered": "Not Neutered/Spayed", "specialNeeds": "Special Needs", "story": "Story", "adoption": "Adoption", "adoptNow": "<PERSON><PERSON><PERSON>", "today": "today", "notFound": "Cat Not Found", "notFoundDescription": "The cat you're looking for could not be found.", "listedCats": "My Cats", "myListedCats": "My Listed Cats", "myFavoriteCats": "My Favorite Cats", "addCat": "Add Cat", "editProfile": "Edit Profile", "dashboard": "Dashboard", "services": "Services", "clinicServices": "Clinic Services", "manageServices": "Manage Services", "addService": "Add Service", "serviceAvailability": {"daily": "Available daily", "appointment": "By appointment only"}, "accountSettings": "Account <PERSON><PERSON>", "joined": "Joined", "publishCat": "Publish a Cat", "startAddingCats": "Start by adding cats that need adoption.", "catDeleted": "Cat listing deleted", "catDeletedDescription": "The cat listing has been removed.", "errorDeletingCat": "Error deleting cat", "errorDeletingCatDescription": "Failed to delete the cat listing. Please try again later.", "errorUpdatingStatusDescription": "Failed to update the cat status. Please try again later.", "confirmDelete": "Delete Cat Listing", "confirmDeleteDescription": "Are you sure you want to delete the listing for {catName}? This action cannot be undone.", "deleting": "Deleting...", "noFavoriteCats": "No favorite cats yet", "noFavoritesYet": "No favorites yet", "startAddingFavorites": "Start adding cats to your favorites to see them here.", "errorLoadingFavorites": "Error loading favorites", "removeFromFavorites": "Remove from favorites", "addToFavorites": "Add to favorites", "favoriteAdded": "Added to favorites", "favoriteAddedDescription": "Cat has been added to your favorites.", "favoriteRemoved": "Removed from favorites", "favoriteRemovedDescription": "Cat has been removed from your favorites.", "noCatsListed": "No cats listed yet", "startListingCats": "Start listing cats for adoption to see them here.", "conversationWith": "Conversation with", "conversations": "conversations", "clinic": {"profileTitle": "Clinic Profile", "profileDescription": "Manage your clinic's public information and settings", "title": "Clinic Profile", "info": "Your clinic's public information", "verified": "Verified", "basicInfo": "Basic Information", "clinicName": "Clinic Name", "clinicNamePlaceholder": "Your clinic name", "city": "City", "cityPlaceholder": "City", "wilaya": "<PERSON><PERSON><PERSON>", "selectWilaya": "Select wilaya", "commune": "<PERSON><PERSON><PERSON>", "selectCommune": "Select commune", "address": "Address", "addressPlaceholder": "Full address", "description": "Description", "descriptionPlaceholder": "Describe your clinic and services", "descriptionHelp": "Tell potential clients about your clinic, specialties, and approach to veterinary care.", "contactInfo": "Contact Information", "phone": "Phone", "phonePlaceholder": "Phone number", "state": "State", "statePlaceholder": "Enter state/province", "email": "Email", "emailPlaceholder": "Contact email", "website": "Website", "websitePlaceholder": "https://your-clinic-website.com", "websiteHelp": "Optional: Your clinic's website URL", "services": "Services", "servicesOffered": "Services Offered", "servicesHelp": "Select the services your clinic offers to help clients find you.", "addServices": "Add Services", "searchServices": "Search services...", "commonServices": "Common Services", "customService": "Add custom service...", "noServicesSelected": "No services selected. Add services to help clients find your clinic.", "operatingHours": "Operating Hours", "addPeriod": "Add Period", "to": "to", "noPeriods": "No time periods set for this day", "closed": "Closed", "note": "Note", "operatingHoursNote": "These hours will be displayed on your public clinic profile. You can add multiple time periods for days with breaks (e.g., lunch break).", "updateProfile": "Update Profile", "createProfile": "Create Profile", "profileUpdated": "Profile updated", "profileUpdatedDescription": "Your clinic profile has been updated successfully.", "profileUpdateError": "Your clinic profile couldn't be updated. Please try again.", "selectWilayaFirst": "Select wilaya first"}, "partners": {"title": "Partner Rescuers", "description": "Rescuers you work with", "noPartners": "No partner rescuers yet", "addPartner": "Add Partner"}, "public": {"title": "Profile", "verified": "Verified", "topHelper": "Top Helper", "sendMessage": "Send Message", "follow": "Follow", "following": "Following", "unfollow": "Unfollow", "memberSince": "Member since", "locationNotSpecified": "Location not specified", "stats": {"catsListed": "Cats Listed", "adoptions": "Adoptions", "reviews": "Reviews", "rating": "Rating"}, "tabs": {"listedCats": "Listed Cats", "reviews": "Reviews", "about": "About"}, "listedCats": {"title": "Listed Cats", "count": "{count} cats", "available": "Available", "adopted": "Adopted", "noListedCats": "No cats listed yet", "noListedCatsDescription": "This user hasn't listed any cats for adoption yet."}, "reviews": {"title": "Reviews", "count": "{count} reviews", "rating": "{rating} out of 5", "basedOn": "Based on {count} reviews", "writeReview": "Write a Review", "noReviews": "No reviews yet", "noReviewsDescription": "This user hasn't received any reviews yet.", "helpful": "found this helpful", "markHelpful": "<PERSON> as Helpful", "reviewBy": "Review by", "anonymous": "Anonymous"}, "about": {"title": "About", "bio": "Bio", "location": "Location", "memberSince": "Member since", "role": "Role", "noBio": "No bio available", "noBioDescription": "This user hasn't added a bio yet.", "activitySummary": "Activity Summary", "stats": {"catsListed": "Cats Listed", "successfulAdoptions": "Successful Adoptions", "averageRating": "Average Rating", "reviewsReceived": "Reviews Received"}}, "roles": {"adopter": "Cat Adopter", "rescuer": "Cat Rescuer", "clinic": "Veterinary Clinic", "admin": "Administrator"}, "notFound": {"title": "User Not Found", "description": "The user profile you're looking for could not be found.", "backToHome": "Back to Home"}}, "messages": {"title": "Messages", "description": "Your conversations", "selectConversation": "Select a conversation", "chooseToStart": "Choose a conversation from the sidebar to start chatting", "noMessagesYet": "No messages yet", "noMessagesDescription": "You don't have any messages yet", "noMessagesStartConversation": "Start a conversation with a cat owner", "searchPlaceholder": "Search conversations...", "noMatchingConversations": "No matching conversations", "unknownUser": "Unknown user", "you": "You", "justNow": "Just now", "minutesAgo": "{minutes} minutes ago", "hoursAgo": "{hours} hours ago", "daysAgo": "{days} days ago", "weeksAgo": "{weeks} weeks ago", "viewAllMessages": "View all messages", "startConversation": "Start the conversation", "sendToBegin": "Send a message to begin chatting with", "conversationWith": "Conversation with", "conversations": "conversations", "comingSoon": "Messages functionality coming soon.", "conversationAbout": "This conversation is about", "chatWith": "Chat with", "with": "with", "typePlaceholder": "Type a message...", "regarding": "Re:", "online": "Online", "lastSeen": "Last seen", "verified": "Verified", "safetyNotice": "Your safety is important. Never share personal information outside the platform.", "new": "New"}, "roles": {"admin": "Admin", "rescuer": "Rescuer", "clinic": "Clinic", "adopter": "Adopter"}, "recommended": {"title": "Recommended Cats", "description": "Cats you might be interested in"}, "addNewService": "Add New Service", "addNewServiceDescription": "Add a new service to your clinic profile"}, "forms": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "submit": "<PERSON><PERSON>", "submitting": "Logging in...", "forgotPassword": "Forgot password?", "noAccount": "Don't have an account?", "register": "Register", "orContinueWith": "Or continue with", "successTitle": "Login successful!", "successMessage": "You are now logged in.", "errorTitle": "<PERSON><PERSON> failed", "errorMessage": "There was a problem logging in. Please try again.", "invalidCredentials": "Invalid email or password.", "githubError": "There was a problem logging in with GitHub.", "googleError": "There was a problem logging in with Google.", "description": "Enter your email and password to access your account"}, "register": {"title": "Create Account", "name": "Full Name", "namePlaceholder": "Your name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "submit": "Create account", "submitting": "Creating account...", "hasAccount": "Already have an account?", "login": "<PERSON><PERSON>", "successTitle": "Registration successful!", "successMessage": "Your account has been created.", "errorTitle": "Registration failed", "errorMessage": "There was a problem creating your account.", "tryAgainMessage": "There was a problem creating your account. Please try again.", "passwordRequirement": "Must be at least 8 characters", "roleLabel": "I am a", "rolePlaceholder": "Select your role", "roleAdopter": "Potential Adopter", "roleRescuer": "Rescuer / Shelter", "roleClinic": "Veterinary Clinic", "roleDescription": "This determines what you can do on the platform", "description": "Join our community to find or list cats for adoption"}, "catForm": {"newTitle": "Add New Cat", "editTitle": "Edit Cat Profile", "steps": {"info": "Cat Info", "images": "Images"}, "info": {"basicInfo": "Basic Information", "name": "Name", "namePlaceholder": "<PERSON>'s name", "age": "Age", "agePlaceholder": "e.g. 2 years, 6 months", "gender": "Gender", "male": "Male", "female": "Female", "breed": "Breed", "breedPlaceholder": "Select a breed", "loadingBreeds": "Loading breeds...", "description": "Description", "descriptionPlaceholder": "Describe the cat's personality, habits, etc.", "story": "Story (Optional)", "storyPlaceholder": "Share the cat's backstory or rescue story", "location": "Location", "wilaya": "<PERSON><PERSON><PERSON>", "wilayaPlaceholder": "Select wilaya", "loadingWilayas": "Loading wilayas...", "commune": "<PERSON><PERSON><PERSON>", "communePlaceholder": "Select commune", "selectWilayaFirst": "Select wilaya first", "loadingCommunes": "Loading communes...", "health": "Health & Status", "vaccinated": "Vaccinated", "neutered": "Neutered/Spayed", "specialNeeds": "Special Needs", "adopted": "Already Adopted", "adoptedDescription": "<PERSON> if the cat has already been adopted", "saveAsDraft": "This will save the cat as a draft (not visible to others)", "nextStep": "Next: Images", "vaccinatedDescription": "<PERSON> has received basic vaccinations", "neuteredDescription": "Cat has been neutered or spayed", "specialNeedsDescription": "Cat has special medical or behavioral needs", "specialNeedsPlaceholder": "Describe the special needs", "storyDescription": "A compelling story can help the cat get adopted faster.", "descriptionDescription": "This will be displayed on the cat's profile."}, "images": {"title": "Cat Photos", "description": "Upload up to 5 high-quality photos of the cat. Click the star icon to set the primary photo.", "maxReached": "Maximum of 5 images reached. Remove an image if you want to add a different one.", "dropzone": "Drag & drop images here, or click to select files", "fileRequirements": "(Max 5 images, 5MB each, JPG, PNG or WebP)", "setPrimary": "Set as primary image", "remove": "Remove image", "primary": "Primary", "add": "Add image", "savePrompt": "Save your changes or publish the cat profile when you're ready.", "noImages": "Please add at least one image of the cat", "tips": {"title": "Image Tips", "tip1": "Use clear, well-lit photos that show the cat clearly", "tip2": "Include at least one photo of the cat's face", "tip3": "Add photos that highlight unique features or personality", "tip4": "Choose photos that are already properly framed and focused"}, "back": "Back to Info"}, "buttons": {"saveDraft": "Save as Draft", "saving": "Saving...", "publish": "Publish Cat", "publishing": "Publishing...", "update": "Update Cat", "updating": "Updating...", "back": "Back"}, "validation": {"nameRequired": "Name is required", "nameLength": "Name must be at least 2 characters", "ageRequired": "Age is required", "ageMax": "Age must be 25 years or less", "breedRequired": "Breed is required", "descriptionRequired": "Description is required", "descriptionLength": "Description must be at least 20 characters", "wilayaRequired": "Wilaya is required", "communeRequired": "Commune is required", "specialNeedsDescriptionRequired": "Please describe the special needs"}, "errors": {"imageSize": "Image size exceeds 5MB limit", "imageFormat": "Invalid image format. Please use JPG, PNG or WebP", "uploadFailed": "Failed to upload image", "maxImages": "Maximum 5 images allowed"}, "success": {"draftSaved": "Draft saved successfully", "published": "Cat published successfully", "updated": "Cat updated successfully"}}}, "theme": {"toggle": "Toggle theme", "light": "Light", "dark": "Dark", "system": "System"}, "language": {"switchLanguage": "Switch language", "english": "English", "french": "French", "arabic": "Arabic"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page", "of": "of", "showing": "Showing", "to": "to", "of_total": "of", "results": "results", "firstPage": "First Page", "lastPage": "Last Page", "morePages": "More pages", "aria": {"previous": "Go to previous page", "next": "Go to next page", "page": "Go to page"}}, "buttons": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "send": "Send", "submit": "Submit", "message": "Message", "loading": "Loading..."}, "services": {"title": "Services", "management": "Service Management", "editService": "Edit Service", "deleteService": "Delete Service", "noServices": "No services listed yet", "noServicesDescription": "Add services that your clinic offers to help cat owners and rescuers.", "showingServices": "Showing {count} services", "appointmentRequired": "Appointment Required", "price": "Price", "pricePlaceholder": "e.g. $50, $100-150, or 'Contact for pricing'", "priceDescription": "Enter the price for this service or leave blank if it varies", "isAvailable": "Service Available", "isAvailableDescription": "Check if this service is currently available to clients", "customDescription": "Custom Description", "customDescriptionPlaceholder": "Add any specific details about how you provide this service...", "customDescriptionDescription": "Optional: Add specific details about how your clinic provides this service", "cancel": "Cancel", "adding": "Adding...", "addService": {"title": "Add New Service", "description": "Add a new service to your clinic profile to help clients find the care they need.", "success": "Service added successfully!", "selectServiceType": "Select Service Type", "searchPlaceholder": "Search service types...", "allCategories": "All Categories", "noServiceTypes": "No service types found. Try adjusting your search or category filter.", "configureService": "Configure Service Details", "submit": "Add Service", "errorLoading": "Error Loading Service Types"}, "categories": {"medical": "Medical", "surgical": "Surgical", "preventive": "Preventive", "emergency": "Emergency", "grooming": "Grooming", "boarding": "Boarding", "consultation": "Consultation", "diagnostic": "Diagnostic", "dental": "Dental", "behavioral": "Behavioral", "nutrition": "Nutrition", "other": "Other", "general": "General", "preventive care": "Preventive Care", "surgery": "Surgery"}, "form": {"serviceName": "Service Name", "serviceNamePlaceholder": "e.g. Vaccinations, Spay/Neuter, etc.", "description": "Description", "descriptionPlaceholder": "Describe the service you offer...", "descriptionHelp": "Provide details about what this service includes.", "category": "Category (optional)", "categoryPlaceholder": "e.g. Surgery, Preventive Care, Emergency", "categoryHelp": "Categorize this service to help organize your offerings.", "price": "Price (optional)", "pricePlaceholder": "e.g. $50, $100-150, or 'Free'", "priceHelp": "Leave blank if price varies or is to be discussed.", "available": "Available", "availableHelp": "Show this service as currently available", "requiresAppointment": "Requires Appointment", "requiresAppointmentHelp": "This service requires scheduling", "addService": "Add Service", "updateService": "Update Service", "adding": "Adding...", "updating": "Updating...", "deleting": "Deleting..."}, "status": {"available": "Available", "unavailable": "Unavailable", "walkInAvailable": "Walk-in Available", "appointmentRequired": "Appointment Required"}, "actions": {"toggleAvailability": "Toggle availability", "editService": "Edit service", "deleteService": "Delete service"}, "messages": {"serviceAdded": "Service added", "serviceAddedDescription": "The service has been added to your profile.", "serviceUpdated": "Service updated", "serviceUpdatedDescription": "The service has been updated successfully.", "serviceDeleted": "Service deleted", "serviceDeletedDescription": "The service has been removed from your profile.", "availabilityUpdated": "Service updated", "availabilityUpdatedDescription": "Service availability has been updated.", "deleteConfirmation": "Are you sure you want to delete the {serviceName} service? This action cannot be undone.", "errorAdding": "Failed to add service. Please try again.", "errorUpdating": "Failed to update service. Please try again.", "errorDeleting": "Failed to delete service. Please try again.", "errorToggling": "Failed to update service. Please try again.", "errorLoading": "Failed to load services", "errorLoadingDescription": "There was an error loading your services."}}, "stats": {"users": "Registered Users", "errors": {"failedToLoad": "Failed to load statistics", "noStats": "No statistics available"}}, "chat": {"successTitle": "Chat started!", "successDescription": "You can now communicate with the cat owner.", "errorTitle": "Something went wrong", "errorDescription": "Unable to start chat. Please try again.", "continueConversation": "Continue Conversation", "continueConversationWith": "Continue chat with {name}", "contactRescuer": "Contact Rescuer", "startConversation": "Start a conversation", "dialogDescription": "Send a message to the cat owner to ask questions or express interest.", "messagePlaceholder": "Type your message here...", "sending": "Sending...", "sendMessage": "Send Message", "cannotMessageYourself": "You cannot message yourself.", "templates": {"general": "Hello, I would like to inquire about your services.", "services": "Could you please provide more information about your available services?", "appointment": "I would like to schedule an appointment. When would be a good time?"}, "templateLabels": {"general": "General Inquiry", "services": "Services", "appointment": "Appointment"}}, "activity": {"youFavorited": "You favorited", "conversationAbout": "Conversation about", "sentYouMessage": "sent you a message"}, "clinics": {"title": "Veterinary Clinics", "description": "Find trusted veterinary clinics and services for your feline companions", "featured": "Featured", "services": "Services", "more": "more", "viewProfile": "View Profile", "todayHours": "Today", "error": "Error", "errorLoadingClinics": "Error loading clinics", "noClinicsFound": "No clinics found", "adjustFilters": "Try adjusting your search filters", "showingResults": "Showing {count} of {total} clinics", "veterinaryClinic": "Veterinary Clinic", "notFound": "Clinic Not Found", "notFoundDescription": "The clinic you're looking for could not be found", "profileDescription": "Veterinary clinic in {location}", "contactClinic": "Contact Clinic", "visitWebsite": "Visit Website", "servicesOffered": "Services Offered", "location": "Location", "operatingHours": "Operating Hours", "clinicInfo": "Clinic Information", "memberSince": "Member since", "status": "Status", "verified": "Verified", "search": {"placeholder": "Search clinics by name or location...", "clear": "Clear search", "tryDifferentTerms": "Try different search terms"}, "filters": {"title": "Filters", "description": "Filter clinics by location, services, and more", "location": "Location", "selectWilaya": "Select Wilaya", "selectCommune": "Select Commune", "featured": "Featured", "featuredOnly": "Featured clinics only", "services": "Services", "reset": "Reset", "apply": "Apply"}, "sort": {"featured": "Featured first", "newest": "Newest first", "oldest": "Oldest first", "nameAsc": "Name A-Z", "nameDesc": "Name Z-A"}}, "admin": {"title": "Admin Dashboard", "description": "Manage users, cats, and site content", "welcome": "Welcome, {name}", "tabs": {"overview": "Overview", "cats": "Cats", "users": "Users", "clinics": "Clinics"}, "stats": {"title": "Overview", "totalUsers": "Total Users", "totalCats": "Total Cats", "totalClinics": "Total Clinics", "totalMessages": "Total Messages", "totalFavorites": "Total Favorites", "thisWeek": "this week", "adoptionStatistics": "Adoption Statistics", "catStatusOverview": "Cat Status Overview", "monthly": "Monthly", "weekly": "Weekly", "weeklyDataNotAvailable": "Weekly data not available in demo", "status": {"available": "Available", "pending": "Pending", "adopted": "Adopted", "unavailable": "Unavailable"}}, "recentCats": {"title": "Recent Cat Listings", "description": "The most recently added cats"}, "recentUsers": {"title": "Recent User Registrations", "description": "The most recently registered users"}, "recentClinics": {"title": "Recent Clinic Registrations", "description": "The most recently registered clinics"}, "cats": {"title": "Manage Cat Listings", "description": "View, approve, edit, or remove cat listings", "searchPlaceholder": "Search cats...", "noCats": "No cats found", "columns": {"cat": "Cat", "owner": "Owner", "location": "Location", "status": "Status", "createdAt": "Created", "yearsOld": "years old"}, "filters": {"status": "Status", "allStatuses": "All Statuses"}, "status": {"available": "Available", "pending": "Pending", "adopted": "Adopted", "unavailable": "Unavailable"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete", "changeStatus": "Change Status"}, "dialogs": {"deleteTitle": "Delete Cat", "deleteDescription": "Are you sure you want to delete this cat? This action cannot be undone.", "statusTitle": "Change Cat Status", "statusDescription": "Select the new status for this cat."}, "changeStatus": "Change Cat Status", "changeStatusDescription": "Change the status of {name}", "deleteCat": "Delete Cat", "deleteCatDescription": "Are you sure you want to delete {name}? This action cannot be undone."}, "users": {"title": "Manage Users", "description": "View and manage user accounts", "searchPlaceholder": "Search users...", "noUsers": "No users found", "roleUpdated": "Role updated", "roleUpdatedDescription": "The role of {name} has been updated.", "columns": {"user": "User", "email": "Email", "role": "Role", "location": "Location", "createdAt": "Joined"}, "table": {"user": "User", "role": "Role", "location": "Location", "joinedDate": "Joined Date"}, "filters": {"role": "Role", "allRoles": "All Roles"}, "actions": {"view": "View Profile", "changeRole": "Change Role", "delete": "Delete User"}, "dialogs": {"deleteTitle": "Delete User", "deleteDescription": "Are you sure you want to delete this user? This action cannot be undone.", "roleTitle": "Change User Role", "roleDescription": "Select the new role for this user."}, "changeRole": "Change User Role", "changeRoleDescription": "Change the role of {name}", "deleteUser": "Delete User", "deleteUserDescription": "Are you sure you want to delete {name}? This action cannot be undone."}, "clinics": {"title": "Manage Clinics", "description": "View and manage veterinary clinic profiles", "searchPlaceholder": "Search clinics...", "noClinics": "No clinics found", "featureNotAvailable": "Feature not available yet", "statusUpdated": "Clinic status updated successfully", "featuredUpdated": "Featured status updated successfully", "clinicDeleted": "Clinic deleted successfully", "columns": {"clinic": "Clinic", "location": "Location", "contact": "Contact", "status": "Status", "featured": "Featured", "createdAt": "Created", "actions": "Actions"}, "actions": {"viewProfile": "View Profile", "edit": "Edit", "changeStatus": "Change Status", "feature": "Feature", "unfeature": "Unfeature", "delete": "Delete"}, "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected"}, "filters": {"status": "Status", "featured": "Featured"}, "dialogs": {"changeStatus": {"title": "Change Clinic Status", "description": "Change the status of {clinicName}", "newStatus": "New Status"}, "edit": {"title": "Edit Clinic Profile", "description": "Edit the profile information for {clinicName}"}, "delete": {"title": "Delete Clinic", "description": "Are you sure you want to delete {clinicName}? This action cannot be undone."}}, "form": {"name": "Clinic Name", "namePlaceholder": "Enter clinic name", "address": "Address", "addressPlaceholder": "Enter clinic address", "phone": "Phone Number", "phonePlaceholder": "Enter phone number", "website": "Website", "websitePlaceholder": "Enter website URL (optional)"}, "profileUpdated": "Clinic profile updated successfully", "bulk": {"selected": "{count} clinic(s) selected", "approve": "Approve Selected", "reject": "Reject Selected", "approved": "{count} clinic(s) approved successfully", "rejected": "{count} clinic(s) rejected successfully", "dialogs": {"approve": {"title": "Approve Selected Clinics", "description": "Are you sure you want to approve {count} selected clinic(s)?"}, "reject": {"title": "Reject Selected Clinics", "description": "Are you sure you want to reject {count} selected clinic(s)?"}}}}, "roles": {"admin": "Administrator", "rescuer": "Rescuer", "clinic": "Clinic", "adopter": "Adopter"}, "status": {"user": {"admin": "Administrator", "rescuer": "Rescuer", "clinic": "Clinic", "adopter": "Adopter"}, "cat": {"available": "Available", "pending": "Pending", "adopted": "Adopted", "unavailable": "Unavailable"}, "application": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected"}}, "table": {"actions": "Actions", "showing": "Showing {start} to {end} of {total} results", "previous": "Previous", "next": "Next", "page": "Page {current} of {total}"}, "filters": {"all": "All"}, "common": {"error": "Error", "cancel": "Cancel", "update": "Update", "updating": "Updating...", "delete": "Delete", "deleting": "Deleting...", "approve": "Approve", "approving": "Approving...", "reject": "Reject", "rejecting": "Rejecting...", "clear": "Clear", "notSpecified": "Not specified"}, "serviceTypes": {"title": "Service Types Management", "description": "Manage global service types that clinics can offer", "createServiceType": "Create Service Type", "createServiceTypeDescription": "Add a new service type that clinics can offer to their clients", "editServiceType": "Edit Service Type", "editServiceTypeDescription": "Update the details of this service type", "deleteServiceType": "Delete Service Type", "deleteServiceTypeDescription": "Are you sure you want to delete '{name}'? This action cannot be undone and will remove this service type from all clinics.", "serviceTypesList": "Service Types List", "name": "Name", "namePlaceholder": "Enter service type name", "descriptionPlaceholder": "Enter detailed description of the service", "descriptionHelp": "Provide a clear description that helps clinics understand what this service entails", "category": "Category", "selectCategory": "Select a category", "displayOrder": "Display Order", "displayOrderHelp": "Lower numbers appear first in lists", "requiresAppointment": "Requires Appointment", "requiresAppointmentHelp": "Check if this service typically requires scheduling an appointment", "isActive": "Active", "isActiveHelp": "Only active service types are available for clinics to add", "status": "Status", "appointment": "Appointment", "createdBy": "Created By", "actions": "Actions", "active": "Active", "inactive": "Inactive", "required": "Required", "notRequired": "Not Required", "create": "Create", "creating": "Creating...", "update": "Update", "updating": "Updating...", "delete": "Delete", "deleting": "Deleting...", "cancel": "Cancel", "createSuccess": "Service type created successfully", "updateSuccess": "Service type updated successfully", "deleteSuccess": "Service type deleted successfully", "toggleSuccess": "Service type status updated successfully", "noServiceTypes": "No service types found", "filterByCategory": "Filter by category", "filterByStatus": "Filter by status", "allCategories": "All Categories", "allStatuses": "All Statuses", "searchPlaceholder": "Search service types...", "categories": {"medical": "Medical", "surgical": "Surgical", "preventive": "Preventive", "emergency": "Emergency", "grooming": "Grooming", "boarding": "Boarding", "consultation": "Consultation", "diagnostic": "Diagnostic", "dental": "Dental", "behavioral": "Behavioral", "nutrition": "Nutrition", "other": "Other"}}}, "breeds": {"Persian": "Persian", "Siamese": "Siamese", "Maine Coon": "Maine Coon", "Ragdoll": "<PERSON><PERSON><PERSON><PERSON>", "Bengal": "Bengal", "Sphynx": "<PERSON><PERSON><PERSON>", "British Shorthair": "British Shorthair", "Abyssinian": "Abyssinian", "Scottish Fold": "Scottish Fold", "Norwegian Forest": "Norwegian Forest", "Siberian": "Siberian", "Russian Blue": "Russian Blue", "Birman": "<PERSON><PERSON><PERSON>", "Domestic Shorthair": "Domestic Shorthair", "Domestic Longhair": "Domestic Longhair", "Mixed Breed": "Mixed Breed"}, "notFound": {"title": "Page Not Found", "subtitle": "Oops! This page seems to have wandered off like a curious cat.", "description": "The page you're looking for doesn't exist. It might have been moved, deleted, or you may have mistyped the URL.", "suggestions": {"title": "Here's what you can do:", "browseCats": "Browse available cats for adoption", "goHome": "Return to the homepage"}, "buttons": {"browseCats": "Browse Cats", "goHome": "Go Home"}}}